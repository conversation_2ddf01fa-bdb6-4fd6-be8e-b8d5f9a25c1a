# 🗑️ Supabase Account Deletion Setup Guide

This guide will help you set up secure account deletion functionality in your Supabase project.

## 📋 Prerequisites

- Supabase project set up
- Supabase CLI installed (`npm install -g supabase`)
- Access to your Supabase dashboard

## 🗄️ Step 1: Database Setup

1. **Open Supabase Dashboard**
   - Go to your project dashboard
   - Navigate to **SQL Editor**

2. **Run the SQL Script**
   - Copy the contents of `supabase-delete-account-setup.sql`
   - Paste it into the SQL Editor
   - Click **Run** to execute

This will create:
- `delete_user_account()` function for secure data deletion
- Proper RLS policies
- Optional soft-delete functionality
- Cleanup functions for maintenance

## 🔧 Step 2: Deploy Edge Function

1. **Initialize Supabase locally** (if not done already):
   ```bash
   supabase init
   ```

2. **Login to Supabase**:
   ```bash
   supabase login
   ```

3. **Link your project**:
   ```bash
   supabase link --project-ref YOUR_PROJECT_REF
   ```

4. **Create the Edge Function directory structure**:
   ```bash
   mkdir -p supabase/functions/delete-account
   ```

5. **Copy the Edge Function**:
   - Copy the contents of `supabase/functions/delete-account/index.ts`
   - Save it to `supabase/functions/delete-account/index.ts` in your project

6. **Deploy the Edge Function**:
   ```bash
   supabase functions deploy delete-account
   ```

## 🔐 Step 3: Environment Variables

The Edge Function automatically uses these Supabase environment variables:
- `SUPABASE_URL` - Your project URL
- `SUPABASE_ANON_KEY` - Your anon/public key  
- `SUPABASE_SERVICE_ROLE_KEY` - Your service role key (for admin operations)

These are automatically available in Edge Functions.

## 🧪 Step 4: Test the Setup

1. **Test the database function** (in SQL Editor):
   ```sql
   -- This should work when called by an authenticated user
   SELECT delete_user_account(auth.uid());
   ```

2. **Test the Edge Function** (using curl):
   ```bash
   curl -X POST 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/delete-account' \
     -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
     -H 'Content-Type: application/json' \
     -d '{"confirmation": "DELETE"}'
   ```

## 🎯 Step 5: Frontend Integration

The frontend code has been updated to call the Edge Function. Make sure your app can:

1. **Get user session** for authentication
2. **Call the Edge Function** with proper headers
3. **Handle responses** and redirect appropriately

## 🔒 Security Features

### ✅ What's Protected:
- **User Authentication**: Only authenticated users can delete accounts
- **Self-Deletion Only**: Users can only delete their own accounts
- **Confirmation Required**: Must send "DELETE" confirmation
- **Transaction Safety**: Database operations are wrapped in transactions
- **Admin Privileges**: Uses service role for auth.users deletion

### 🛡️ RLS Policies:
- Users can only delete their own data
- Function execution requires authentication
- Database-level security enforcement

## 📊 Monitoring & Maintenance

### View Deletion Logs:
```sql
SELECT * FROM account_deletion_logs;
```

### Cleanup Old Soft-Deleted Data:
```sql
SELECT cleanup_deleted_accounts();
```

### Monitor Edge Function Logs:
- Go to Supabase Dashboard → Edge Functions → delete-account → Logs

## 🚨 Important Notes

1. **Irreversible Action**: Account deletion cannot be undone
2. **Data Backup**: Consider backing up important data before deletion
3. **Cascade Deletions**: Ensure all related data is properly deleted
4. **Rate Limiting**: Consider adding rate limiting for the Edge Function
5. **Audit Trail**: The system logs all deletion attempts

## 🔧 Customization

### Add More Data Tables:
Edit the `delete_user_account()` function to include additional tables:
```sql
-- Add these lines in the function
DELETE FROM your_other_table WHERE user_id = user_id_to_delete;
DELETE FROM another_table WHERE user_id = user_id_to_delete;
```

### Soft Delete vs Hard Delete:
- **Current**: Hard delete (immediate removal)
- **Alternative**: Uncomment the soft delete trigger for grace period

### Grace Period:
Modify the cleanup function to change the 30-day grace period:
```sql
-- Change 30 days to your preferred period
AND deleted_at < NOW() - INTERVAL '30 days'
```

## 🆘 Troubleshooting

### Common Issues:

1. **"Function not found"**: Ensure the SQL script ran successfully
2. **"Unauthorized"**: Check RLS policies and authentication
3. **"Edge Function error"**: Check function logs in dashboard
4. **"Service role key"**: Ensure it's properly set in Supabase

### Debug Steps:
1. Check Supabase logs in dashboard
2. Test database function directly in SQL Editor
3. Verify Edge Function deployment
4. Check browser network tab for errors

## 📞 Support

If you encounter issues:
1. Check Supabase documentation
2. Review function logs in dashboard
3. Test each component individually
4. Verify all permissions and policies
