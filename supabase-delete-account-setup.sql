-- =====================================================
-- Supabase Account Deletion Setup Script
-- =====================================================
-- Run this script in your Supabase SQL Editor
-- This sets up the necessary functions and policies for account deletion

-- 1. Create a function to delete user data and account
CREATE OR REPLACE FUNCTION delete_user_account(user_id_to_delete UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    deleted_drawings_count INTEGER := 0;
    result JSON;
BEGIN
    -- Check if the requesting user is the same as the user to be deleted
    -- This ensures users can only delete their own accounts
    IF auth.uid() != user_id_to_delete THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Unauthorized: You can only delete your own account'
        );
    END IF;

    -- Start transaction
    BEGIN
        -- Delete user's drawings from the 'draw' table
        -- Update this table name to match your actual table name
        DELETE FROM draw 
        WHERE user_id = user_id_to_delete;
        
        GET DIAGNOSTICS deleted_drawings_count = ROW_COUNT;

        -- Add any other user data deletions here
        -- For example, if you have other tables:
        -- DELETE FROM user_preferences WHERE user_id = user_id_to_delete;
        -- DELETE FROM user_sessions WHERE user_id = user_id_to_delete;

        -- Delete the user from auth.users (this requires admin privileges)
        -- Note: This will be handled by the Edge Function instead
        
        -- Return success response
        result := json_build_object(
            'success', true,
            'message', 'User data deleted successfully',
            'deleted_drawings', deleted_drawings_count
        );

        RETURN result;

    EXCEPTION WHEN OTHERS THEN
        -- Rollback transaction on error
        RAISE;
        RETURN json_build_object(
            'success', false,
            'error', 'Failed to delete user data: ' || SQLERRM
        );
    END;
END;
$$;

-- 2. Create RLS policy to allow users to delete their own data
-- This ensures the function can only be called by the account owner
CREATE POLICY "Users can delete their own account data" ON draw
    FOR DELETE USING (auth.uid() = user_id);

-- 3. Grant necessary permissions
-- Allow authenticated users to execute the function
GRANT EXECUTE ON FUNCTION delete_user_account(UUID) TO authenticated;

-- 4. Create a trigger to soft-delete instead of hard-delete (optional)
-- This is useful if you want to keep data for a grace period
CREATE OR REPLACE FUNCTION soft_delete_user_data()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Instead of deleting, mark as deleted and set deletion timestamp
    UPDATE draw 
    SET 
        is_deleted = true,
        deleted_at = NOW()
    WHERE user_id = OLD.user_id;
    
    -- Prevent the actual DELETE
    RETURN NULL;
END;
$$;

-- Uncomment the following lines if you want to use soft delete instead
-- CREATE TRIGGER soft_delete_user_trigger
--     BEFORE DELETE ON draw
--     FOR EACH ROW
--     EXECUTE FUNCTION soft_delete_user_data();

-- 5. Create a cleanup function for permanently deleting soft-deleted data
-- Run this periodically to clean up old deleted accounts
CREATE OR REPLACE FUNCTION cleanup_deleted_accounts()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    cleanup_count INTEGER := 0;
BEGIN
    -- Delete data that has been soft-deleted for more than 30 days
    DELETE FROM draw 
    WHERE is_deleted = true 
    AND deleted_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    
    RETURN cleanup_count;
END;
$$;

-- 6. Create a view for account deletion logs (optional)
CREATE OR REPLACE VIEW account_deletion_logs AS
SELECT 
    user_id,
    deleted_at,
    COUNT(*) as deleted_items_count
FROM draw 
WHERE is_deleted = true
GROUP BY user_id, deleted_at
ORDER BY deleted_at DESC;

-- 7. Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_draw_user_id_deleted 
ON draw(user_id, is_deleted, deleted_at);

-- =====================================================
-- Usage Instructions:
-- =====================================================
-- 
-- 1. Run this script in Supabase SQL Editor
-- 2. Create the Edge Function (see next file)
-- 3. Update your frontend to call the Edge Function
-- 
-- To test the function manually:
-- SELECT delete_user_account(auth.uid());
-- 
-- To cleanup old deleted data:
-- SELECT cleanup_deleted_accounts();
-- =====================================================
