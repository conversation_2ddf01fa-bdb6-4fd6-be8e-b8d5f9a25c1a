import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { deleteAccount } from "@/db/auth";
import { toast } from "sonner";
import { useNavigate } from "@tanstack/react-router";
import { Trash2, AlertTriangle } from "lucide-react";

interface DeleteAccountDialogProps {
  isOpen: boolean;
  onClose: () => void;
  userEmail: string;
}

export function DeleteAccountDialog({ 
  isOpen, 
  onClose, 
  userEmail 
}: DeleteAccountDialogProps) {
  const [confirmationText, setConfirmationText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const navigate = useNavigate();

  const handleDeleteAccount = async () => {
    if (confirmationText !== "DELETE") {
      toast.error("Please type 'DELETE' to confirm");
      return;
    }

    setIsDeleting(true);
    
    try {
      const { error } = await deleteAccount();
      
      if (error) {
        toast.error("Failed to delete account", {
          description: error instanceof Error ? error.message : "An error occurred"
        });
        setIsDeleting(false);
        return;
      }

      toast.success("Account deletion initiated", {
        description: "You have been signed out. Your account will be deleted shortly."
      });
      
      // Close dialog and navigate to home
      onClose();
      navigate({ to: "/" });
    } catch (error) {
      toast.error("An unexpected error occurred");
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      setConfirmationText("");
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <Card className="w-full max-w-md mx-4">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-2">
            <AlertTriangle className="h-12 w-12 text-destructive" />
          </div>
          <CardTitle className="text-destructive">Delete Account</CardTitle>
          <CardDescription>
            This action cannot be undone. This will permanently delete your account and all associated data.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Account to be deleted:</Label>
            <div className="p-2 bg-muted rounded text-sm font-mono">
              {userEmail}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="confirmation">
              Type <span className="font-bold text-destructive">DELETE</span> to confirm:
            </Label>
            <Input
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder="Type DELETE here"
              disabled={isDeleting}
            />
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p>This will:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Delete your account permanently</li>
              <li>Remove all your drawings and data</li>
              <li>Sign you out immediately</li>
            </ul>
          </div>
        </CardContent>
        
        <CardFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isDeleting}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteAccount}
            disabled={confirmationText !== "DELETE" || isDeleting}
            isLoading={isDeleting}
            loadingText="Deleting..."
            className="flex-1"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Account
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
