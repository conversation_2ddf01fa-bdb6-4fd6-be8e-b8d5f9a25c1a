import { supabase } from "./supabase";

export async function login(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email: email,
    password: password,
  });
  return { data, error };
}

export async function signUp(name: string, email: string, password: string) {
  const { data, error } = await supabase.auth.signUp({
    email: email,
    password: password,
    options: {
      data: {
        name: name,
      },
    },
  });
  return { data, error };
}

export async function logout() {
  const { error } = await supabase.auth.signOut();

  return { error };
}

export async function getLocalUser() {
  const { data, error } = await supabase.auth.getSession();

  return { data, error };
}

export async function getUser() {
  const { data, error } = await supabase.auth.getUser();

  return { data, error };
}

export async function updateUser(name: string, email: string) {
  const { data, error } = await supabase.auth.updateUser({
    email: email,
    data: {
      name: name,
    },
  });
  return { data, error };
}

export async function signInWithGoogle() {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${window.location.origin}/pages`,
    },
  });
  return { data, error };
}

export async function deleteAccount() {
  try {
    // Get the current session to get the access token
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return { data: null, error: sessionError || new Error("No active session") };
    }

    // Call the Supabase Edge Function to delete the account
    const { data, error } = await supabase.functions.invoke('delete-account', {
      body: { confirmation: 'DELETE' },
      headers: {
        Authorization: `Bearer ${session.access_token}`,
      },
    });

    if (error) {
      return { data: null, error };
    }

    if (!data.success) {
      return { data: null, error: new Error(data.error || "Failed to delete account") };
    }

    // Account deletion was successful, the user is automatically signed out
    return { data: data, error: null };
  } catch (error) {
    return { data: null, error };
  }
}
