import { supabase } from "./supabase";

export async function login(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email: email,
    password: password,
  });
  return { data, error };
}

export async function signUp(name: string, email: string, password: string) {
  const { data, error } = await supabase.auth.signUp({
    email: email,
    password: password,
    options: {
      data: {
        name: name,
      },
    },
  });
  return { data, error };
}

export async function logout() {
  const { error } = await supabase.auth.signOut();

  return { error };
}

export async function getLocalUser() {
  const { data, error } = await supabase.auth.getSession();

  return { data, error };
}

export async function getUser() {
  const { data, error } = await supabase.auth.getUser();

  return { data, error };
}

export async function updateUser(name: string, email: string) {
  const { data, error } = await supabase.auth.updateUser({
    email: email,
    data: {
      name: name,
    },
  });
  return { data, error };
}

export async function signInWithGoogle() {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${window.location.origin}/pages`,
    },
  });
  return { data, error };
}

export async function deleteAccount() {
  try {
    // Note: This will sign out the user and mark their account for deletion
    // The actual deletion happens on the server side
    const { error } = await supabase.auth.signOut();

    if (error) {
      return { data: null, error };
    }

    // In a real implementation, you would call a server-side function
    // or use Supabase Edge Functions to handle account deletion
    // For now, we'll just sign out the user
    return { data: { message: "Account deletion initiated" }, error: null };
  } catch (error) {
    return { data: null, error };
  }
}
